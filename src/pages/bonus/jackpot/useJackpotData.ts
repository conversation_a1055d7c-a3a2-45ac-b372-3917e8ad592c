import No1 from '~/pages/bonus/imgs/jackpot-no1.svg';
import No2 from '~/pages/bonus/imgs/jackpot-no2.svg';
import No3 from '~/pages/bonus/imgs/jackpot-no3.svg';

const bp = useAppBreakpoints();
export function useJackpotData() {
  const rulesConfigQry = useQuery({
    queryKey: [apis.apiActivityAviatorJackpotConfig.id],
    queryFn: () => apis.apiActivityAviatorJackpotConfig(undefined),
  });

  // 响应式参数
  const queryParams = ref(1);

  const subjectQry = useQuery({
    queryKey: [apis.apiActivityAviatorJackpotRankings.id, queryParams],
    queryFn: () => apis.apiActivityAviatorJackpotRankings({ rank_type: queryParams.value }),
  });

  const rulesConfigData = computed(() => rulesConfigQry.data.value?.rules ?? []);
  const subjectLive = computed(() => subjectQry.data.value?.live ?? []);
  const subjectHistory = computed(() => subjectQry.data.value?.history ?? []);
  const subjectLiveList = computed(() => {
    if (bp.ltTablet)
      return [subjectQry.data.value?.live?.list];
    return splitArrayByIndexParity(subjectQry.data.value?.live?.list ?? []);
  });

  const subjectHistoryList = computed(() => {
    if (bp.ltTablet)
      return [subjectQry.data.value?.history?.list];
    return splitArrayByIndexParity(subjectQry.data.value?.history?.list ?? []);
  });

  const subjectLiveTopThree = computed(() => {
    if (bp.ltTablet) {
      return subjectLiveList.value[0].slice(0, 3);
    }
    else {
      return [
        subjectLiveList.value[0][0],
        subjectLiveList.value[1][0],
        subjectLiveList.value[0][1],
      ];
    }
  });

  return {
    rulesConfigQry,
    subjectQry,
    rulesConfigData,
    subjectLive,
    subjectHistory,
    queryParams,
    subjectLiveList,
    subjectHistoryList,
    subjectLiveTopThree,
  };
}

export const icons = reactive([
  No1,
  No2,
  No3,
]);

export const cols = computed(() => bp.tablet ? 2 : 1);

/**
 * 将数组按照单数索引和双数索引分成二维数组
 * @param arr 原数组
 * @returns [[偶数索引数组], [奇数索引数组]]
 *
 * @example
 * splitArrayByIndexParity([1, 2, 3, 4, 5, 6, 7, 8, 9])
 * // 返回: [[1, 3, 5, 7, 9], [2, 4, 6, 8]]
 */
export function splitArrayByIndexParity<T>(arr: T[]): T[][] {
  const evenIndexItems: T[] = [];
  const oddIndexItems: T[] = [];

  arr.forEach((item, index) => {
    if (index % 2 === 0) {
      evenIndexItems.push(item);
    }
    else {
      oddIndexItems.push(item);
    }
  });

  return [evenIndexItems, oddIndexItems];
}
