<script setup lang="ts">
import { cols, icons, useJackpotData } from '../useJackpotData';

const { subjectHistoryList, subjectHistory } = useJackpotData();

// 滚动同步逻辑
const leftTableContainer = ref<HTMLElement>();
const rightTableContainer = ref<HTMLElement>();
let isScrolling = false;

// 同步滚动函数
function syncScroll(sourceElement: HTMLElement, targetElement: HTMLElement) {
  if (isScrolling)
    return;

  isScrolling = true;
  targetElement.scrollTop = sourceElement.scrollTop;

  // 使用 requestAnimationFrame 确保滚动完成后重置标志
  requestAnimationFrame(() => {
    isScrolling = false;
  });
}

// 设置滚动监听
onMounted(async () => {
  await nextTick();

  if (leftTableContainer.value && rightTableContainer.value) {
    // 左表格容器滚动时同步右表格容器
    leftTableContainer.value.addEventListener('scroll', () => {
      if (rightTableContainer.value) {
        syncScroll(leftTableContainer.value!, rightTableContainer.value);
      }
    });

    // 右表格容器滚动时同步左表格容器
    rightTableContainer.value.addEventListener('scroll', () => {
      if (leftTableContainer.value) {
        syncScroll(rightTableContainer.value!, leftTableContainer.value);
      }
    });
  }
});
</script>

<template>
  <!-- history -->
  <div class="jackpot-subject-history">
    <p class="title lt-tablet:hidden">
      History
    </p>
    <div class="rounded-lg bg-sys-layer-a px-7.5 py-3.5 text-center text-sys-text-body">
      <p class="text-base">
        Jackpot
      </p>
      <h5 class="text-5 text-#18A349">
        <TNum :value="(subjectHistory?.jackpot_amount ?? 0)" :decimals="2" format="original" />
      </h5>
    </div>
    <div class="twins-table">
      <div class="twins-table-th">
        <div class="twins-table_title">
          <span class="text-left">Ranking</span>
          <span>Username</span>
          <span class="text-right">Bet Volume</span>
        </div>
        <div class="twins-table_title">
          <span class="text-left">Ranking</span>
          <span>Username</span>
          <span class="text-right">Bet Volume</span>
        </div>
      </div>
      <div class="twins-table_content">
        <div v-for="(item, key) in cols" :key>
          <div v-for="(i, k) of subjectHistoryList[key]" :key="k" class="flex-between">
            <div class="text-left">
              <img v-if="Number(i.rank_no <= 3)" :src="icons[i.rank_no - 1]" alt="" class="w-4 object-cover">
              <span v-else>{{ i.rank_no }}</span>
            </div>
            <div>{{ i?.user?.nickname }}</div>
            <div class="text-right">
              <TNum :value="(i?.bet_amount ?? 0)" :decimals="2" format="fixed-dec" />
            </div>
          </div>
        </div>
      </div>
      <div class="divider-v-gradual h-auto" />
    </div>

    <div class="aggregate">
      <div class="aggregate-left">
        {{ subjectHistory.my_rank?.rank_no ?? 0 }}
      </div>
      <div class="divider-v-gradual" />
      <div class="aggregate-center">
        <p>My Bets: {{ subjectHistory?.my_rank?.bet_amount ?? 0 }}</p>
        <p>My Rewards: <TNum :value="((subjectHistory?.my_rank?.reward_rate ?? 0) * 100)" :decimals="2" format="original" /> %</p>
      </div>
      <div class="divider-v-gradual" />
      <div class="aggregate-rigth">
        <p>Ranks Left</p>
        <p>{{ subjectHistory?.my_rank?.bet_amount_need ?? 0 }}</p>
      </div>
    </div>
  </div>
</template>

<style>
@import '../style.scss';
</style>
